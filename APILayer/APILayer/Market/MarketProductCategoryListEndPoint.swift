//
//  MarketProductCategoryListEndPoint.swift
//  APILayer
//
//  Created by Augment Agent on 24/08/2025.
//

import RxSwift
import Storage
import Networking
import SharedData

public struct MarketProductCategoryListEndPoint: NetworkEndpoint {
    
    public static var service = MarketProductCategoryListEndPoint()
    
    public var path: String = "/market/product/category/list"
    public var method: RequestMethod = .post
    public var encoding: RequestEncoding = .json
    public var headers: RequestHeaders?
    
    public struct Request: Codable {
        public init() {}
    }
    
    public struct Response: Codable {
        public let list: [ProductCategory]
    }
    
    public struct ProductCategory: Codable {
        public let categoryId: Int
        public let categoryName: String
        public let children: [CategoryChild]
        public let instruments: [FundInstrument]
    }
    
    public struct CategoryChild: Codable {
        public let categoryId: Int
        public let categoryName: String
        public let instruments: [FundInstrument]
    }
    
    public struct FundInstrument: Codable {
        public let instrumentId: Int
        public let instrumentName: String
        public let symbol: String
        public let exchange: String
        public let currency: String
        public let logo: String?
        public let price: String?
        public let priceChange: String?
        public let priceChangeRate: String?
        public let allocationClass: String?
        public let riskLevel: String?
        public let period: String?
    }
    
    public mutating func call() -> Observable<Response> {
        request(parameters: Request())
    }
}
